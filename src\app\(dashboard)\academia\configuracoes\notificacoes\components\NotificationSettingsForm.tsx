'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Loader2, Save } from 'lucide-react';
import { useNotificationSettings } from '../hooks/useNotificationSettings';
import { NotificationTypesSection } from './NotificationTypesSection';
import { QuietHoursSection } from './QuietHoursSection';

const notificationSettingsSchema = z.object({
  emailFromName: z.string().min(1, 'Nome do remetente é obrigatório'),
  emailEnabled: z.boolean(),
  whatsappEnabled: z.boolean(),
  whatsappOptInMessage: z.string().optional(),
  quietHoursStart: z.string(),
  quietHoursEnd: z.string(),
  timezone: z.string(),
  notificationTypes: z.record(z.object({
    email: z.boolean(),
    whatsapp: z.boolean(),
    in_app: z.boolean(),
  })),
});

type NotificationSettingsFormData = z.infer<typeof notificationSettingsSchema>;

export function NotificationSettingsForm() {
  const { 
    settings, 
    isLoading, 
    isSaving, 
    updateSettings 
  } = useNotificationSettings();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isDirty }
  } = useForm<NotificationSettingsFormData>({
    resolver: zodResolver(notificationSettingsSchema),
    defaultValues: {
      emailFromName: '',
      emailEnabled: true,
      whatsappEnabled: true,
      whatsappOptInMessage: '',
      quietHoursStart: '22:00',
      quietHoursEnd: '08:00',
      timezone: 'America/Sao_Paulo',
      notificationTypes: {
        payment: { email: true, whatsapp: false, in_app: true },
        class: { email: true, whatsapp: false, in_app: true },
        system: { email: false, whatsapp: false, in_app: true },
        enrollment: { email: true, whatsapp: false, in_app: true },
        event: { email: true, whatsapp: false, in_app: true }
      }
    }
  });

  const emailEnabled = watch('emailEnabled');
  const whatsappEnabled = watch('whatsappEnabled');

  useEffect(() => {
    if (settings) {
      setValue('emailFromName', settings.emailFromName);
      setValue('emailEnabled', settings.emailEnabled);
      setValue('whatsappEnabled', settings.whatsappEnabled);
      setValue('whatsappOptInMessage', settings.whatsappOptInMessage || '');
      setValue('quietHoursStart', settings.quietHours.start.slice(0, 5)); // Remove seconds
      setValue('quietHoursEnd', settings.quietHours.end.slice(0, 5)); // Remove seconds
      setValue('timezone', settings.quietHours.timezone);
      setValue('notificationTypes', settings.notificationTypes);
    }
  }, [settings, setValue]);

  const onSubmit = async (data: NotificationSettingsFormData) => {
    try {
      await updateSettings({
        emailFromName: data.emailFromName,
        emailEnabled: data.emailEnabled,
        whatsappEnabled: data.whatsappEnabled,
        whatsappOptInMessage: data.whatsappOptInMessage,
        quietHoursStart: `${data.quietHoursStart}:00`,
        quietHoursEnd: `${data.quietHoursEnd}:00`,
        timezone: data.timezone,
        notificationTypes: data.notificationTypes,
      });
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Carregando configurações...</span>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {/* Configurações Gerais */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Configurações Gerais</h3>
          <p className="text-sm text-muted-foreground">
            Configure as informações básicas para envio de notificações.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="emailFromName">Nome do Remetente (E-mail)</Label>
            <Input
              id="emailFromName"
              {...register('emailFromName')}
              placeholder="Ex: Academia Apex"
            />
            {errors.emailFromName && (
              <p className="text-sm text-destructive">{errors.emailFromName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="timezone">Fuso Horário</Label>
            <Select value={watch('timezone')} onValueChange={(value) => setValue('timezone', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione o fuso horário" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="America/Sao_Paulo">São Paulo (GMT-3)</SelectItem>
                <SelectItem value="America/Manaus">Manaus (GMT-4)</SelectItem>
                <SelectItem value="America/Rio_Branco">Rio Branco (GMT-5)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Separator />

      {/* Canais de Notificação */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Canais de Notificação</h3>
          <p className="text-sm text-muted-foreground">
            Ative ou desative os canais de comunicação disponíveis.
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="emailEnabled">Notificações por E-mail</Label>
              <p className="text-sm text-muted-foreground">
                Enviar notificações via e-mail para alunos e responsáveis
              </p>
            </div>
            <Switch
              id="emailEnabled"
              checked={emailEnabled}
              onCheckedChange={(checked) => setValue('emailEnabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="whatsappEnabled">Notificações por WhatsApp</Label>
              <p className="text-sm text-muted-foreground">
                Enviar notificações via WhatsApp (requer configuração adicional)
              </p>
            </div>
            <Switch
              id="whatsappEnabled"
              checked={whatsappEnabled}
              onCheckedChange={(checked) => setValue('whatsappEnabled', checked)}
            />
          </div>

          {whatsappEnabled && (
            <div className="ml-6 space-y-2">
              <Label htmlFor="whatsappOptInMessage">Mensagem de Opt-in (WhatsApp)</Label>
              <Input
                id="whatsappOptInMessage"
                {...register('whatsappOptInMessage')}
                placeholder="Ex: Olá! Você gostaria de receber notificações da academia via WhatsApp?"
              />
              <p className="text-xs text-muted-foreground">
                Mensagem enviada para solicitar permissão antes de enviar notificações via WhatsApp
              </p>
            </div>
          )}
        </div>
      </div>

      <Separator />

      {/* Horários de Silêncio */}
      <QuietHoursSection 
        startTime={watch('quietHoursStart')}
        endTime={watch('quietHoursEnd')}
        onStartTimeChange={(time) => setValue('quietHoursStart', time)}
        onEndTimeChange={(time) => setValue('quietHoursEnd', time)}
      />

      <Separator />

      {/* Tipos de Notificação */}
      <NotificationTypesSection 
        notificationTypes={watch('notificationTypes')}
        onNotificationTypesChange={(types) => setValue('notificationTypes', types)}
        emailEnabled={emailEnabled}
        whatsappEnabled={whatsappEnabled}
      />

      {/* Botão de Salvar */}
      <div className="flex justify-end">
        <Button 
          type="submit" 
          disabled={!isDirty || isSaving}
          className="min-w-[120px]"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Salvando...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Salvar
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
